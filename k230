import time
import os
import math
from machine import FPIOA, UART
from media.sensor import *
from media.display import *
from media.media import *

print("=== 程序开始执行 ===")
print("导入模块完成")

# 任务标志位
Flag = 3
print(f"初始Flag值: {Flag}")

# 阈值参数
BLACK_THRESHOLD = (0, 73)
BLACK_THRESHOLD2 = (0, 100)

# 检测参数
MIN_PIXELS = 11
AREA_THRESHOLD = 50
max_density = 0.22
min_density = 0.1
max_solidity = 0.31
max_convexity = 0.61
min_convexity = 0.45
min_area = 2800
max_area = 73000

# 全局变量

#激光替代点
target_x = 318
target_y = 220
intersection = (0, 0)
flag1 = 0
flag2 = 0
last_compensated_x = 0
last_compensated_y = 0
last_rect_width = 0
last_rect_height = 0

# 激光检测参数
BLUE_LASER_THRESHOLD = (89, 100, -18, 12, -27, 14)

# 显示参数
DISPLAY_WIDTH = 640
DISPLAY_HEIGHT = 480


# 圆环跟踪参数
CIRCLE_STEPS = 40
current_step = 0
circle_target_points = []
tracking_active = False
step_tolerance = 15

# 补偿参数
compensation_history = []
MAX_HISTORY = 4  # 平衡性能和精度
MAX_COMPENSATION_X = 25
MAX_COMPENSATION_Y = 15

# 物理尺寸参数
A4_WIDTH_MM = 210
A4_HEIGHT_MM = 297
BLACK_FRAME_WIDTH_MM = 18
TOTAL_WIDTH_MM = A4_WIDTH_MM + BLACK_FRAME_WIDTH_MM * 2
TOTAL_HEIGHT_MM = A4_HEIGHT_MM + BLACK_FRAME_WIDTH_MM * 2

# 像素转换比例
PIXEL_PER_MM_X = 640 / TOTAL_WIDTH_MM
PIXEL_PER_MM_Y = 480 / TOTAL_HEIGHT_MM

# 圆环参数
TARGET_RADIUS_MM = 20
AVERAGE_PIXEL_PER_MM = (PIXEL_PER_MM_X + PIXEL_PER_MM_Y) / 2
TARGET_RADIUS_PX = TARGET_RADIUS_MM * AVERAGE_PIXEL_PER_MM
CIRCLE_POINTS = 40
CIRCLE_FIT_FACTOR = 0.6

# 硬件初始化
sensor = Sensor(width=1280, height=960)
sensor.reset()
sensor.set_framesize(width=640, height=480)
sensor.set_pixformat(Sensor.GRAYSCALE)

fpioa = FPIOA()
fpioa.set_function(3, FPIOA.UART1_TXD)
fpioa.set_function(4, FPIOA.UART1_RXD)
uart = UART(UART.UART1, baudrate=9600)

Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
MediaManager.init()
time.sleep(1)
sensor.run()
clock = time.clock()

def setup_sensor_for_grayscale_detection():
    try:
        sensor.set_framesize(width=640, height=480)
        sensor.set_pixformat(Sensor.GRAYSCALE)
        time.sleep_ms(20)
        return True
    except:
        return False

#__________________________________________灰度透射功能函数_____________________________________________

def calculate_distance(p1, p2):
    return math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)

def calculate_center(points):
    if not points:
        return (0, 0)
    sum_x = sum(p[0] for p in points)
    sum_y = sum(p[1] for p in points)
    return (sum_x / len(points), sum_y / len(points))

# 发挥第三问圆环生成函数
def generate_adaptive_circle_points(center_x, center_y, rect_width=None, rect_height=None):
    """基于矩形尺寸生成更贴合的自适应椭圆轨迹点"""
    points = []
    try:
        # 如果没有提供矩形尺寸，使用默认圆形
        if rect_width is None or rect_height is None:
            radius_x = TARGET_RADIUS_PX
            radius_y = TARGET_RADIUS_PX
        else:
            # 更贴合的椭圆半径计算方法
            # 直接基于矩形尺寸，而不是固定的基础半径

            # 使用矩形尺寸的一定比例作为椭圆半径
            radius_x = rect_width * CIRCLE_FIT_FACTOR * 0.5   # 宽度的60%作为X轴半径
            radius_y = rect_height * CIRCLE_FIT_FACTOR * 0.5  # 高度的60%作为Y轴半径

            # 限制最小和最大半径，避免过小或过大
            min_radius = 15  # 最小半径15像素
            max_radius = 80  # 最大半径80像素

            radius_x = max(min_radius, min(max_radius, radius_x))
            radius_y = max(min_radius, min(max_radius, radius_y))

            # 如果矩形长宽比过于极端，进行适当调整
            aspect_ratio = max(radius_x, radius_y) / max(min(radius_x, radius_y), 1)
            if aspect_ratio > 2.0:  # 长宽比超过2:1时，适当调整
                if radius_x > radius_y:
                    radius_x = radius_y * 1.8  # 限制最大长宽比为1.8:1
                else:
                    radius_y = radius_x * 1.8

        # 生成椭圆轨迹点
        for i in range(CIRCLE_POINTS):
            angle = 2 * math.pi * i / CIRCLE_POINTS
            x = center_x + radius_x * math.cos(angle)
            y = center_y + radius_y * math.sin(angle)
            points.append((int(x), int(y)))

        return points
    except:
        return []

def detect_blue_laser_point(img):
    """检测蓝色激光点"""
    try:
        blue_blobs = img.find_blobs(
            [BLUE_LASER_THRESHOLD],
            pixels_threshold=5,
            area_threshold=5,
            merge=True
        )

        if blue_blobs:
            # 选择面积最大的蓝色色块作为激光点
            largest_blob = max(blue_blobs, key=lambda b: b.area())
            return (largest_blob.cx(), largest_blob.cy())
        return None
    except:
        return None

def send_unified_control_data(motor_rotation=0, error_x=0, error_y=0, pid_control=0, laser_enable=0):
    try:
        # 确保error_x和error_y是整数
        error_x = int(error_x)
        error_y = int(error_y)

        # 处理负数（使用补码表示）
        if error_x < 0:
            error_x = error_x + 65536  # 16位补码
        if error_y < 0:
            error_y = error_y + 65536  # 16位补码

        error_x_high = (error_x >> 8) & 0xFF
        error_x_low = error_x & 0xFF
        error_y_high = (error_y >> 8) & 0xFF
        error_y_low = error_y & 0xFF

        data = bytearray([
            0x2C, 0x12,
            motor_rotation & 0xFF,
            error_x_high, error_x_low,
            error_y_high, error_y_low,
            pid_control & 0xFF,
            laser_enable & 0xFF,
            0x5B
        ])
        uart.write(data)

        # 调试信息（仅在Flag==3时打印）
        if Flag == 3:
            print(f"UART发送: motor={motor_rotation}, ex={error_x-65536 if error_x>32767 else error_x}, ey={error_y-65536 if error_y>32767 else error_y}, pid={pid_control}, laser={laser_enable}")

    except Exception as e:
        print(f"串口发送错误: {e}")
        pass

def send_circle_trajectory(points):
    """发送圆环轨迹数据"""
    try:
        if not points:
            return
        count = len(points)
        msg = f"$$C,{count},"
        for x, y in points:
            msg += f"{x},{y},"
        msg = msg.rstrip(',') + "##"
        uart.write(msg.encode())
    except:
        pass

#任务标志位改变函数
def Flag_transform():
    global Flag
    try:
        # 读取所有可用数据
        data = uart.read()
        if data is not None and len(data) >= 3:
            # 查找有效的数据包
            for i in range(len(data) - 2):
                if data[i] == 0xAA and data[i+2] == 0xAB:
                    old_flag = Flag
                    Flag = data[i+1]
                    if old_flag != Flag:
                        print(f"Flag改变: {old_flag} -> {Flag}")
                    break
    except Exception as e:
        print(f"Flag转换错误: {e}")
        pass


# 任务函数

#Flag==2 - 基础部分第二题
def task_basic_2():
    """基础部分第二题：2秒内瞄准靶心，D1≤2cm"""
    global flag1, flag2

    try:
        # 捕获图像（传感器已在主循环中配置）
        img = sensor.snapshot()

        # 首先尝试小阈值检测
        flag1 = detect_target(img, BLACK_THRESHOLD)

        # 如果小阈值检测失败，尝试中阈值检测
        if not flag1:
            img = sensor.snapshot()  # 重新捕获图像
            flag2 = detect_target(img, BLACK_THRESHOLD2)

        # 显示FPS
        fps_text = f'FPS: {clock.fps():.2f}'
        img.draw_string_advanced(10, 10, 30, fps_text, color=(255, 255, 255))

        # 显示状态
        status_text = f"检测状态: {'成功' if (flag1 or flag2) else '失败'}"
        img.draw_string_advanced(10, 50, 20, status_text, color=(0, 255, 0) if (flag1 or flag2) else (255, 0, 0))

        # 显示图像
        Display.show_image(img)

    except:
        pass

#Flag==3 - 基础部分第三题
def task_basic_3():
    """基础部分第三题：4秒内自动瞄准，D1≤2cm"""
    global flag1,flag2,target_x,target_y,last_compensated_x,last_compensated_y

    try:
        print(f"[DEBUG] Task3执行中, Flag={Flag}")

        # 捕获图像（传感器已在主循环中配置）
        img = sensor.snapshot()

        # 首先尝试小阈值检测
        flag1 = detect_target(img, BLACK_THRESHOLD)
        print(f"[DEBUG] flag1检测结果: {flag1}")

        # 如果小阈值检测失败，尝试中阈值检测
        if not flag1:
            img = sensor.snapshot()  # 重新捕获图像
            flag2 = detect_target(img, BLACK_THRESHOLD2)
            print(f"[DEBUG] flag2检测结果: {flag2}")
        else:
            flag2 = False

        # 显示FPS
        fps_text = f'FPS: {clock.fps():.2f}'
        img.draw_string_advanced(10, 10, 30, fps_text, color=(255, 255, 255))

        # 显示状态
        status_text = f"检测状态: {'成功' if (flag1 or flag2) else '失败'}"
        img.draw_string_advanced(10, 50, 20, status_text, color=(0, 255, 0) if (flag1 or flag2) else (255, 0, 0))

        # 显示图像
        Display.show_image(img)

        # Flag 3: 统一通信逻辑
        print(f"[DEBUG] 检测条件: flag1={flag1}, flag2={flag2}")

        if flag1 or flag2:
            print("[DEBUG] 进入检测成功分支")

            # 检查变量是否已初始化
            if 'last_compensated_x' not in globals() or 'last_compensated_y' not in globals():
                print("[ERROR] last_compensated_x/y 未初始化!")
                last_compensated_x = target_x
                last_compensated_y = target_y

            print(f"[DEBUG] 补偿坐标: ({last_compensated_x}, {last_compensated_y})")
            print(f"[DEBUG] 目标坐标: ({target_x}, {target_y})")

            # 计算误差：黄色十字（补偿后中心点）减去黑色箭头（激光替代点）
            e_x = last_compensated_x - target_x  # 黄色十字 - 黑色箭头
            e_y = last_compensated_y - target_y # 黄色十字 - 黑色箭头

            print(f"=== Flag3误差输出 === x={e_x}, y={e_y} ===")

            # 发送数据：找到矩形(motor=0)，坐标误差，开启PID(pid=1)，激光根据误差决定
            send_unified_control_data(motor_rotation=0, error_x=e_x, error_y=e_y, pid_control=1, laser_enable=0)

            # 检查是否到达目标（误差小于15像素）
            distance = (e_x**2 + e_y**2)**0.5
            print(f"[DEBUG] 距离: {distance}")
            if distance < 15:
                print("[DEBUG] 距离小于15，开启激光")
                send_unified_control_data(motor_rotation=0, error_x=e_x, error_y=e_y, pid_control=1, laser_enable=1)
        else:
            print("[DEBUG] 进入检测失败分支，发送电机旋转信号")
            # 没有检测到矩形，发送电机旋转信号
            send_unified_control_data(motor_rotation=1, error_x=0, error_y=0, pid_control=0, laser_enable=1)

    except Exception as e:
        print(f"Task3错误: {e}")
        import traceback
        traceback.print_exc()
        pass


#__________________________________________灰度透射功能函数_____________________________________________

def line_intersection(line1, line2):
    """计算两条直线的交点坐标"""
    (x1, y1, x2, y2) = line1
    (x3, y3, x4, y4) = line2

    # 计算分母（判断是否平行）
    den = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
    if abs(den) < 1e-8:  # 使用小数值避免除零错误
        # 分母为0，两直线平行或重合，返回中点
        return (abs(x2-x1)//2 + min(x1,x2), abs(y2-y1)//2 + min(y1,y2))

    # 计算分子
    t_num = (x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)
    t = t_num / den

    # 计算交点坐标
    x = x1 + t * (x2 - x1)
    y = y1 + t * (y2 - y1)

    return (int(x), int(y))

def apply_compensation(intersection_x, intersection_y, target_x, target_y):
    """跟随矩形移动方向的同向补偿算法"""
    global compensation_history

    try:
        # 添加当前检测点到历史记录
        current_point = (intersection_x, intersection_y)
        compensation_history.append(current_point)

        # 保持历史记录长度
        if len(compensation_history) > 4:
            compensation_history.pop(0)

        # 计算矩形移动方向和速度
        movement_vector_x = 0
        movement_vector_y = 0
        movement_speed = 0

        if len(compensation_history) >= 2:
            if len(compensation_history) >= 3:
                points = compensation_history[-3:]
                movement_vector_x = (points[2][0] - points[0][0]) / 2
                movement_vector_y = (points[2][1] - points[0][1]) / 2
            else:
                points = compensation_history[-2:]
                movement_vector_x = points[1][0] - points[0][0]
                movement_vector_y = points[1][1] - points[0][1]

            movement_speed = math.sqrt(movement_vector_x**2 + movement_vector_y**2)

        # 计算当前偏差
        current_deviation_x = intersection_x - target_x
        current_deviation_y = intersection_y - target_y
        deviation_distance = math.sqrt(current_deviation_x**2 + current_deviation_y**2)

        # 关键改变：补偿量与矩形移动同向 - 减小补偿强度
        if movement_speed > 0.8:  # 降低移动阈值，但矩形在移动时
            # 补偿方向 = 矩形移动方向
            # 大幅减小补偿强度，避免过度补偿

            # 根据移动速度调整补偿强度 - 进一步微调
            if movement_speed > 4:
                movement_factor = 0.12  # 从0.2减小到0.12
            elif movement_speed > 2:
                movement_factor = 0.08  # 从0.12减小到0.08
            else:
                movement_factor = 0.05  # 从0.08减小到0.05

            # 根据偏差距离调整补偿强度 - 进一步微调
            if deviation_distance > 25:
                distance_factor = 0.4   # 从0.6减小到0.4
            elif deviation_distance > 15:
                distance_factor = 0.3   # 从0.4减小到0.3
            elif deviation_distance > 8:
                distance_factor = 0.2   # 从0.25减小到0.2
            else:
                distance_factor = 0.08  # 从0.1减小到0.08

            # 补偿量 = 矩形移动方向 × 移动因子 × 距离因子
            compensation_x = movement_vector_x * movement_factor * distance_factor
            compensation_y = movement_vector_y * movement_factor * distance_factor

            # 最小补偿阈值 - 避免微小抖动
            min_compensation = 0.5
            if abs(compensation_x) < min_compensation:
                compensation_x = 0
            if abs(compensation_y) < min_compensation:
                compensation_y = 0

        else:  # 矩形静止或微动时，使用传统偏差补偿 - 微调
            # 基础补偿系数 - 微调减小
            if deviation_distance > 20:
                base_factor = 0.05  # 从0.08减小到0.05
            elif deviation_distance > 10:
                base_factor = 0.03  # 从0.05减小到0.03
            else:
                base_factor = 0.015 # 从0.02减小到0.015

            compensation_x = current_deviation_x * base_factor
            compensation_y = current_deviation_y * base_factor

        # 进一步限制补偿量 - 微调最大补偿范围
        max_comp_x = min(MAX_COMPENSATION_X, 5)   # 限制X方向最大补偿为5像素
        max_comp_y = min(MAX_COMPENSATION_Y, 4)   # 限制Y方向最大补偿为4像素
        compensation_x = max(-max_comp_x, min(max_comp_x, compensation_x))
        compensation_y = max(-max_comp_y, min(max_comp_y, compensation_y))

        # 平滑处理 - 避免补偿跳跃
        if hasattr(apply_compensation, 'last_comp_x'):
            # 使用指数平滑
            smooth_factor = 0.7
            compensation_x = compensation_x * smooth_factor + apply_compensation.last_comp_x * (1 - smooth_factor)
            compensation_y = compensation_y * smooth_factor + apply_compensation.last_comp_y * (1 - smooth_factor)

        # 存储当前补偿量用于下次平滑
        apply_compensation.last_comp_x = compensation_x
        apply_compensation.last_comp_y = compensation_y

        # 应用补偿：补偿点朝着矩形移动的同一方向移动
        compensated_x = intersection_x + compensation_x  # 注意：这里是加号，与矩形同向
        compensated_y = intersection_y + compensation_y

        return int(compensated_x), int(compensated_y)
    except:
        return intersection_x, intersection_y


def generate_circle_target_points(circle_points):
    """
    从圆环点生成40个目标点
    直接使用40个圆环点作为目标点，完美对应
    """
    global circle_target_points

    if not circle_points or len(circle_points) != CIRCLE_POINTS:
        return []

    # 直接使用圆环点作为目标点，40个点完美对应
    circle_target_points = circle_points.copy()
    return circle_target_points

def get_next_target_point():
    """获取下一个目标点"""
    global current_step

    if not circle_target_points:
        return None

    target_point = circle_target_points[current_step]
    return target_point

def advance_to_next_step():
    """前进到下一步"""
    global current_step
    current_step = (current_step + 1) % CIRCLE_STEPS

def calculate_laser_to_target_error(laser_pos, target_pos):
    """
    计算蓝色激光点到目标点的误差
    返回误差坐标
    """
    if laser_pos is None or target_pos is None:
        return None

    # 计算误差：目标点减去蓝色激光点
    error_x = target_pos[0] - laser_pos[0]  # 目标点 - 蓝色激光点
    error_y = target_pos[1] - laser_pos[1]  # 目标点 - 蓝色激光点

    return (error_x, error_y)


def check_target_reached(laser_pos, target_pos):
    """检查是否到达目标点"""
    if laser_pos is None or target_pos is None:
        return False

    distance = math.sqrt((laser_pos[0] - target_pos[0])**2 + (laser_pos[1] - target_pos[1])**2)
    return distance <= step_tolerance

def reset_tracking():
    """重置跟踪状态"""
    global current_step, tracking_active, circle_target_points
    current_step = 0
    tracking_active = False
    circle_target_points = []

def is_tracking_complete():
    """检查是否完成一圈跟踪"""
    return current_step >= CIRCLE_STEPS

def process_blob(img, blob):
    """处理单个色块 - 完全按照灰度透射原版逻辑"""
    global intersection, target_x, target_y, flag, last_compensated_x, last_compensated_y, last_rect_width, last_rect_height

    try:
        # 获取统计信息
        statistics = img.get_statistics(roi=blob.rect())

        # 绘制主轴和次轴 - 完全按照灰度透射原版
        img.draw_line(blob.major_axis_line(), color=(255, 0, 0))
        img.draw_line(blob.minor_axis_line(), color=(0, 255, 0))

        # 计算轴线交点
        intersection = line_intersection(blob.major_axis_line(), blob.minor_axis_line())

        # 计算对角线交点
        corners = blob.min_corners()
        if len(corners) >= 4:
            duijiao_l1 = (corners[0][0], corners[0][1], corners[2][0], corners[2][1])
            duijiao_l2 = (corners[1][0], corners[1][1], corners[3][0], corners[3][1])
            intersection_2 = line_intersection(duijiao_l1, duijiao_l2)
        else:
            intersection_2 = intersection

        # 计算长短轴比例
        major_line = blob.major_axis_line()
        minor_line = blob.minor_axis_line()

        l1 = math.sqrt((major_line[2] - major_line[0])**2 + (major_line[3] - major_line[1])**2)
        l2 = math.sqrt((minor_line[2] - minor_line[0])**2 + (minor_line[3] - minor_line[1])**2)

        if l1 > 0:
            ratio = l2 / l1
        else:
            ratio = 0

        # 检查是否符合圆形特征
        if ratio > 0.68 and statistics.mode() > 95:
            # 绘制检测结果
            img.draw_rectangle(blob.rect(), color=(255, 0, 0))

            # 绘制角点
            for corner in corners:
                img.draw_cross(corner[0], corner[1], color=(0, 255, 0))

            # 绘制外接圆
            img.draw_circle(blob.enclosing_circle(), color=(255, 255, 0), thickness=2, fill=False)

            # 绘制检测到的中心点 - 改为小的空心圆
            img.draw_circle(int(intersection_2[0]), int(intersection_2[1]), 3, color=(255, 0, 255), thickness=1, fill=False)  # 检测到的中心点（紫色小空心圆）
            img.draw_cross(target_x, target_y, color=(0, 0, 0))  # 目标点（黑色）

            # 计算并绘制补偿后的点 - 改为更小的十字
            compensated_x, compensated_y = apply_compensation(int(intersection_2[0]), int(intersection_2[1]), target_x, target_y)
            img.draw_cross(compensated_x, compensated_y, color=(255, 255, 0), size=4, thickness=1)  # 补偿后的点（黄色，更小更细）
            #print(compensated_x,compensated_y)

            # 存储补偿后的坐标供串口发送函数使用
            last_compensated_x = compensated_x
            last_compensated_y = compensated_y

            # 存储矩形尺寸信息
            last_rect_width = blob.w()
            last_rect_height = blob.h()

            flag = True
            return True
        else:
            flag = False
            return False

    except:
        flag = False
        return False



def detect_target(img, threshold):
    """检测目标"""
    try:
        # 查找符合阈值的色块
        blobs = img.find_blobs([threshold],
                              pixels_threshold=MIN_PIXELS,
                              area_threshold=AREA_THRESHOLD,
                              x_stride=1,
                              y_stride=1,
                              margin=15)

        if not blobs:
            return False

        valid_blobs = []
        for blob in blobs:
            # 检查密度、面积、实心度、凸度
            if (min_density < blob.density() < max_density and
                blob.area() > min_area and
                blob.solidity() < max_solidity and
                blob.convexity() < max_convexity):

                img.draw_rectangle(blob.rect(), color=(255, 0, 0))
                valid_blobs.append(blob)

        if valid_blobs:
            # 处理第一个有效色块
            return process_blob(img, valid_blobs[0])
        else:
            return False

    except:
        return False

#_________________________________________任务函数_____________________________________________

#Flag==4,5 - 发挥部分第一、二题
def task_advanced_1_2():
    """发挥部分第一、二题：N=1/2圈，t≤20s/40s，D1≤2cm"""
    global flag1, flag2

    try:
        # 捕获图像（传感器已在主循环中配置）
        img = sensor.snapshot()

        # 检测蓝色激光点
        blue_laser_pos = detect_blue_laser_point(img)
        if blue_laser_pos:
            # 绘制蓝色激光点 - 改为小矩形框
            x, y = blue_laser_pos
            img.draw_rectangle(x-3, y-3, 6, 6, color=(128, 0, 255), thickness=1)  # 蓝紫色小矩形框

        # 首先尝试小阈值检测
        flag1 = detect_target(img, BLACK_THRESHOLD)

        # 如果小阈值检测失败，尝试中阈值检测
        if not flag1:
            img = sensor.snapshot()  # 重新捕获图像
            flag2 = detect_target(img, BLACK_THRESHOLD2)

        # 显示FPS
        fps_text = f'FPS: {clock.fps():.2f}'
        img.draw_string_advanced(10, 10, 30, fps_text, color=(255, 255, 255))

        # 显示状态
        status_text = f"检测状态: {'成功' if (flag1 or flag2) else '失败'}"
        img.draw_string_advanced(10, 50, 20, status_text, color=(0, 255, 0) if (flag1 or flag2) else (255, 0, 0))

        # 显示图像
        Display.show_image(img)

        # Flag 4-5: 统一通信逻辑
        if (flag1 or flag2) and blue_laser_pos:
            # 检测成功且有蓝色激光点，计算误差：黄色十字（补偿后中心点）减去蓝色激光点
            target_pos = (last_compensated_x, last_compensated_y)
            error_x = target_pos[0] - blue_laser_pos[0]  # 黄色十字 - 蓝色激光点
            error_y = target_pos[1] - blue_laser_pos[1]  # 黄色十字 - 蓝色激光点

            # 发送数据：电机不旋转(motor=0)，蓝紫激光到矩形中心误差，开启PID(pid=1)，激光关闭(laser=0)
            send_unified_control_data(motor_rotation=0, error_x=error_x, error_y=error_y,
                                    pid_control=1, laser_enable=0)

    except:
        pass

#Flag==6 - 发挥部分第三题
def task_advanced_3():
    """发挥部分第三题：基于物理尺寸的圆环轨迹 + 蓝色激光点跟踪"""
    global flag1, flag2, tracking_active, circle_target_points, current_step

    try:
        # 捕获图像（使用灰度格式）
        img = sensor.snapshot()

        # 检测蓝色激光点
        blue_laser_pos = detect_blue_laser_point(img)
        if blue_laser_pos:
            # 绘制蓝色激光点 - 改为小矩形框
            x, y = blue_laser_pos
            img.draw_rectangle(x-3, y-3, 6, 6, color=(128, 0, 255), thickness=1)  # 蓝紫色小矩形框

        # 首先尝试小阈值检测
        flag1 = detect_target(img, BLACK_THRESHOLD)

        # 如果小阈值检测失败，尝试中阈值检测
        if not flag1:
            img = sensor.snapshot()  # 重新捕获图像
            flag2 = detect_target(img, BLACK_THRESHOLD2)

        # 如果检测成功，绘制物理尺寸的圆环
        if flag1 or flag2:
            # 使用补偿后的坐标作为圆心
            if 'last_compensated_x' in globals() and 'last_compensated_y' in globals():
                center_x = last_compensated_x
                center_y = last_compensated_y

                # 生成基于矩形尺寸的自适应圆环点
                circle_points = generate_adaptive_circle_points(center_x, center_y, last_rect_width, last_rect_height)

                # 绘制圆环轨迹（紫色小圆点） - 缩小一半
                for point in circle_points:
                    if 0 <= point[0] < 640 and 0 <= point[1] < 480:  # 边界检查
                        img.draw_circle(point[0], point[1], 1, color=(255, 0, 255), thickness=1)

                # 生成60个目标点
                target_points = generate_circle_target_points(circle_points)

                if target_points and blue_laser_pos:
                    # 激活跟踪模式
                    tracking_active = True

                    # 只绘制当前目标点，减少视觉干扰
                    current_target = target_points[current_step] if current_step < len(target_points) else None
                    if current_target:
                        img.draw_circle(current_target[0], current_target[1], 5, color=(255, 255, 0), thickness=-1)

                    # 获取当前目标点
                    current_target = get_next_target_point()
                    if current_target:
                        # 绘制激光点到目标点的连线
                        img.draw_line(blue_laser_pos[0], blue_laser_pos[1],
                                    current_target[0], current_target[1],
                                    color=(255, 255, 0), thickness=1)

                        # Flag 6: 统一通信逻辑
                        error = calculate_laser_to_target_error(blue_laser_pos, current_target)
                        if error:
                            # 发送数据：电机不旋转(motor=0)，蓝紫激光到目标点误差，开启PID(pid=1)，激光关闭(laser=0)
                            send_unified_control_data(motor_rotation=0, error_x=error[0], error_y=error[1],
                                                    pid_control=1, laser_enable=0)

                            # 检查是否到达目标点
                            if check_target_reached(blue_laser_pos, current_target):
                                advance_to_next_step()

                                # 检查是否完成一圈
                                if is_tracking_complete():
                                    reset_tracking()

                # 绘制圆心（蓝色大圆点）
                img.draw_circle(center_x, center_y, 4, color=(0, 0, 255), thickness=3)

                # 发送圆环轨迹数据
                send_circle_trajectory(circle_points)

        # 最简化显示信息
        fps_text = f'Task6 FPS: {clock.fps():.1f}'
        img.draw_string_advanced(10, 10, 16, fps_text, color=(255, 255, 255))

        if tracking_active:
            img.draw_string_advanced(10, 30, 14, f"Step:{current_step}/{CIRCLE_STEPS}", color=(0, 255, 255))

        # 显示补偿信息
        if 'last_compensated_x' in globals() and 'last_compensated_y' in globals():
            comp_text = f"补偿中心: ({last_compensated_x},{last_compensated_y})"
            img.draw_string_advanced(10, 130, 16, comp_text, color=(0, 255, 255))

            # 显示移动趋势信息（简化版）
            if len(compensation_history) >= 2:
                recent_points = compensation_history[-2:]
                movement_x = recent_points[1][0] - recent_points[0][0]
                movement_y = recent_points[1][1] - recent_points[0][1]
                movement_speed = math.sqrt(movement_x**2 + movement_y**2)
                trend_text = f"移动趋势: ({movement_x:.1f},{movement_y:.1f}) 速度:{movement_speed:.1f}"
                img.draw_string_advanced(10, 150, 16, trend_text, color=(255, 128, 0))

        # 显示图像
        Display.show_image(img)

    except:
        pass

# 主程序

# 全局变量跟踪当前传感器状态
current_sensor_mode = None

while True:
    try:
        clock.tick()
        os.exitpoint()

        # 串口数据处理
        if uart.any():
            Flag_transform()

        # 根据Flag配置传感器（只在切换时配置）
        if Flag in [0, 2, 3, 4, 5, 6] and current_sensor_mode != "grayscale":
            setup_sensor_for_grayscale_detection()
            current_sensor_mode = "grayscale"

        # 每隔一段时间打印当前Flag状态（用于调试）
        if hasattr(clock, 'frame_count'):
            clock.frame_count += 1
        else:
            clock.frame_count = 1

        if clock.frame_count % 30 == 0:  # 每30帧打印一次
            print(f"[MAIN] 当前Flag: {Flag}")

        # 主要功能逻辑
        if Flag == 0:
            # 正常拍照模式
            img = sensor.snapshot()
            img.draw_string_advanced(10, 10, 16, "Flag: 0 - Normal Mode", color=(255, 255, 255))
            img.draw_string_advanced(10, 30, 16, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
            Display.show_image(img)

        #E题基本要求(2)：2秒内瞄准靶心，D1≤2cm
        elif Flag == 2:
            task_basic_2()

        #E题基本要求(3)：4秒内自动瞄准，D1≤2cm
        elif Flag == 3:
            print(f"[MAIN] 执行Flag==3, 调用task_basic_3()")
            task_basic_3()
        #E题发挥(1)：N=1圈，t≤20s，D1≤2cm
        elif Flag == 4:
            task_advanced_1_2()

        #E题发挥(2)：N=2圈，t≤40s，D1≤2cm
        elif Flag == 5:
            task_advanced_1_2()

        #发挥部分第三题：基于物理尺寸的圆环轨迹
        elif Flag == 6:
            task_advanced_3()

    except:
        # 错误恢复：重置到安全状态
        time.sleep_ms(100)

